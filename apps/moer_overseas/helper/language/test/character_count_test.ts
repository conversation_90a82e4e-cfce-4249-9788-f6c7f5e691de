// Simple test to verify character counting logic
function testCharacterCounting() {
  const testCases = [
    {
      text: 'Hello teacher I want to register for the course',
      expectedChinese: 0,
      expectedEnglish: 39, // Count letters only
      description: 'Pure English text'
    },
    {
      text: '老师好，我想要报名课程',
      expectedChinese: 10,
      expectedEnglish: 0,
      description: 'Pure Chinese text'
    },
    {
      text: 'Hello 老师',
      expectedChinese: 2,
      expectedEnglish: 5,
      description: 'Mixed text'
    }
  ]

  testCases.forEach(testCase => {
    let chineseCount = 0
    let englishCount = 0
    
    // Remove non-Chinese/English characters
    const cleanText = testCase.text.replace(/[^\u4e00-\u9fff\u3400-\u4dbfa-zA-Z]/g, '')
    
    for (const char of cleanText) {
      // Check for Chinese characters (fixed range)
      if (/[\u4e00-\u9fff\u3400-\u4dbf]/.test(char)) {
        chineseCount++
      }
      // Check for English characters
      else if (/[a-zA-Z]/.test(char)) {
        englishCount++
      }
    }

    console.log(`\n${testCase.description}:`)
    console.log(`Original: "${testCase.text}"`)
    console.log(`Clean: "${cleanText}"`)
    console.log(`Chinese: ${chineseCount} (expected: ${testCase.expectedChinese})`)
    console.log(`English: ${englishCount} (expected: ${testCase.expectedEnglish})`)
    console.log(`Total: ${cleanText.length}`)
    
    if (cleanText.length > 0) {
      const chineseRatio = chineseCount / cleanText.length
      const englishRatio = englishCount / cleanText.length
      console.log(`Chinese ratio: ${(chineseRatio * 100).toFixed(1)}%`)
      console.log(`English ratio: ${(englishRatio * 100).toFixed(1)}%`)
    }
  })
}

// Run the test
testCharacterCounting()
