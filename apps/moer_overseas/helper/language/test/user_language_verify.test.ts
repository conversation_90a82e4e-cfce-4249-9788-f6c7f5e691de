import { UserLanguage } from '../user_language_verify'
import { ChatStateStore } from 'service/local_cache/chat_state_store'
import { chatDBClient, chatHistoryServiceClient, chatStateStoreClient } from '../../../service/instance'

// Mock LLM to avoid API calls in tests
jest.mock('lib/ai/llm/llm_model', () => ({
  LLM: jest.fn().mockImplementation(() => ({
    predict: jest.fn().mockResolvedValue('NO_SWITCH')
  }))
}))

describe('UserLanguage.verify Tests', () => {

  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks()
  })

  describe('Basic Language Detection', () => {
    it('should detect Chinese language when Chinese characters > 60%', async () => {
      // Mock checkLanguageSwitchRequest to return false (no switch request)
      const mockCheckLanguageSwitchRequest = jest.spyOn(UserLanguage, 'checkLanguageSwitchRequest')
      mockCheckLanguageSwitchRequest.mockResolvedValue(false)

      // Mock dependencies
      chatStateStoreClient.increaseNodeCount = jest.fn()
      chatStateStoreClient.getNodeCount = jest.fn().mockResolvedValue(3) // Trigger detection (3 % 3 === 0)
      chatHistoryServiceClient.getUserMessages = jest.fn().mockResolvedValue([
        '老师好，我想要报名课程',
        '请问什么时候开始上课？',
        '我对冥想很感兴趣'
      ])
      chatStateStoreClient.getFlags = jest.fn().mockResolvedValue({ language: undefined })
      chatStateStoreClient.update = jest.fn()

      await UserLanguage.verify('test_chat_1')

      expect(chatStateStoreClient.update).toHaveBeenCalledWith('test_chat_1', {
        state: { language: UserLanguage.Language_ZH }
      })

      mockCheckLanguageSwitchRequest.mockRestore()
    })

    it('should detect English language when English characters > 60%', async () => {
      // Mock checkLanguageSwitchRequest to return false (no switch request)
      const mockCheckLanguageSwitchRequest = jest.spyOn(UserLanguage, 'checkLanguageSwitchRequest')
      mockCheckLanguageSwitchRequest.mockResolvedValue(false)

      // Mock dependencies
      chatStateStoreClient.increaseNodeCount = jest.fn()
      chatStateStoreClient.getNodeCount = jest.fn().mockResolvedValue(6) // Trigger detection (6 % 3 === 0)
      chatHistoryServiceClient.getUserMessages = jest.fn().mockResolvedValue([
        '老师好，I want to register for the course',
        'When does the meditation class start today',
        'I am very interested in learning meditation techniques'
      ])
      chatStateStoreClient.getFlags = jest.fn().mockResolvedValue({ language: undefined })
      chatStateStoreClient.update = jest.fn()

      await UserLanguage.verify('test_chat_2')

      expect(chatStateStoreClient.update).toHaveBeenCalledWith('test_chat_2', {
        state: { language: UserLanguage.Language_EN }
      })

      mockCheckLanguageSwitchRequest.mockRestore()
    })

    it('should not change language when neither language reaches 60% threshold', async () => {
      // Mock checkLanguageSwitchRequest to return false (no switch request)
      const mockCheckLanguageSwitchRequest = jest.spyOn(UserLanguage, 'checkLanguageSwitchRequest')
      mockCheckLanguageSwitchRequest.mockResolvedValue(false)

      // Mock dependencies
      chatStateStoreClient.increaseNodeCount = jest.fn()
      chatStateStoreClient.getNodeCount = jest.fn().mockResolvedValue(9) // Trigger detection (9 % 3 === 0)
      chatHistoryServiceClient.getUserMessages = jest.fn().mockResolvedValue([
        'Hello 老师',
        '////////////////////////////////////',
        '！！！！！！！！！！！！！！！！！！！！'
      ])
      chatStateStoreClient.getFlags = jest.fn().mockResolvedValue({ language: undefined })
      chatStateStoreClient.update = jest.fn()

      await UserLanguage.verify('test_chat_3')

      expect(chatStateStoreClient.update).not.toHaveBeenCalled()

      mockCheckLanguageSwitchRequest.mockRestore()
    })
  })

  describe('Message Filtering', () => {
    it('should filter out image messages starting with 【普通图片】', async () => {
      // Mock checkLanguageSwitchRequest to return false (no switch request)
      const mockCheckLanguageSwitchRequest = jest.spyOn(UserLanguage, 'checkLanguageSwitchRequest')
      mockCheckLanguageSwitchRequest.mockResolvedValue(false)

      // Mock dependencies
      chatStateStoreClient.increaseNodeCount = jest.fn()
      chatStateStoreClient.getNodeCount = jest.fn().mockResolvedValue(3)
      chatHistoryServiceClient.getUserMessages = jest.fn().mockResolvedValue([
        '【普通图片】这是一张显示课程安排的图片，包含了详细的时间表和课程内容介绍',
        'Hello teacher I want to register',
        '【视频】这是一个关于冥想练习的视频',
        'When does the class start?'
      ])
      chatStateStoreClient.getFlags = jest.fn().mockResolvedValue({ language: undefined })
      chatStateStoreClient.update = jest.fn()

      await UserLanguage.verify('test_chat_filter')

      // Should detect English since image/video descriptions are filtered out
      expect(chatStateStoreClient.update).toHaveBeenCalledWith('test_chat_filter', {
        state: { language: UserLanguage.Language_EN }
      })

      mockCheckLanguageSwitchRequest.mockRestore()
    })

    it('should skip detection when filtered messages are too short', async () => {
      // Mock checkLanguageSwitchRequest to return false (no switch request)
      const mockCheckLanguageSwitchRequest = jest.spyOn(UserLanguage, 'checkLanguageSwitchRequest')
      mockCheckLanguageSwitchRequest.mockResolvedValue(false)

      // Mock dependencies
      chatStateStoreClient.increaseNodeCount = jest.fn()
      chatStateStoreClient.getNodeCount = jest.fn().mockResolvedValue(3)
      chatHistoryServiceClient.getUserMessages = jest.fn().mockResolvedValue([
        '【普通图片】图片描述',
        '【视频】视频内容',
        'ok',
        'yes'
      ])
      chatStateStoreClient.getFlags = jest.fn().mockResolvedValue({ language: undefined })
      chatStateStoreClient.update = jest.fn()

      await UserLanguage.verify('test_chat_short')

      // Should not update language due to insufficient characters after filtering
      expect(chatStateStoreClient.update).not.toHaveBeenCalled()

      mockCheckLanguageSwitchRequest.mockRestore()
    })
  })

  describe('Language Stability', () => {
    it('should require higher threshold to switch from existing language', async () => {
      // Mock dependencies - user currently set to Chinese
      chatStateStoreClient.increaseNodeCount = jest.fn()
      chatStateStoreClient.getNodeCount = jest.fn().mockResolvedValue(3)
      chatHistoryServiceClient.getUserMessages = jest.fn().mockResolvedValue([
        'Hello teacher, I want to register for course', // 65% English
        '你说什么屁话呢',
        '有多远滚多远，哪凉快哪呆着去'
      ])
      chatStateStoreClient.getFlags = jest.fn().mockResolvedValue({ language: UserLanguage.Language_ZH })
      chatStateStoreClient.update = jest.fn()

      await UserLanguage.verify('test_chat_stability')

      // Should NOT switch to English because 65% < 70% (switch threshold)
      expect(chatStateStoreClient.update).not.toHaveBeenCalled()
    })

    it('should switch language when threshold is high enough', async () => {
      // Mock dependencies - user currently set to Chinese
      chatStateStoreClient.increaseNodeCount = jest.fn()
      chatStateStoreClient.getNodeCount = jest.fn().mockResolvedValue(3)
      chatHistoryServiceClient.getUserMessages = jest.fn().mockResolvedValue([
        'Hello teacher, I want to register for the meditation course',
        'When does the five day meditation practice start?',
        'I am very interested in learning meditation techniques',
        'Thank you very much for your help and guidance'
      ])
      chatStateStoreClient.getFlags = jest.fn().mockResolvedValue({ language: UserLanguage.Language_ZH })
      chatStateStoreClient.update = jest.fn()

      await UserLanguage.verify('test_chat_switch')

      // Should switch to English because > 70% English
      expect(chatStateStoreClient.update).toHaveBeenCalledWith('test_chat_switch', {
        state: { language: UserLanguage.Language_EN }
      })
    })
  })

  describe('Detection Timing', () => {
    it('should only detect on every 3rd message', async () => {
      // Mock dependencies
      chatStateStoreClient.increaseNodeCount = jest.fn()
      chatStateStoreClient.getNodeCount = jest.fn().mockResolvedValue(2) // Not divisible by 3
      chatHistoryServiceClient.getUserMessages = jest.fn()
      chatStateStoreClient.update = jest.fn()

      await UserLanguage.verify('test_chat_timing')

      // Should not call getUserMessages or update since it's not the 3rd message
      expect(chatHistoryServiceClient.getUserMessages).not.toHaveBeenCalled()
      expect(chatStateStoreClient.update).not.toHaveBeenCalled()
    })

    it('should stop detection after 12 messages', async () => {
      // Mock dependencies
      chatStateStoreClient.increaseNodeCount = jest.fn()
      chatStateStoreClient.getNodeCount = jest.fn().mockResolvedValue(15) // > 12 messages
      chatHistoryServiceClient.getUserMessages = jest.fn()
      chatStateStoreClient.update = jest.fn()

      await UserLanguage.verify('test_chat_limit')

      // Should not detect after 12 messages
      expect(chatHistoryServiceClient.getUserMessages).not.toHaveBeenCalled()
      expect(chatStateStoreClient.update).not.toHaveBeenCalled()
    })
  })

  describe('Language Switch Request Detection', () => {
    it('should detect and handle language switch request', async () => {
      // Mock language switch detection
      const mockCheckLanguageSwitchRequest = jest.spyOn(UserLanguage, 'checkLanguageSwitchRequest')
      mockCheckLanguageSwitchRequest.mockResolvedValue(true)

      chatStateStoreClient.increaseNodeCount = jest.fn()
      chatStateStoreClient.getNodeCount = jest.fn()
      chatHistoryServiceClient.getUserMessages = jest.fn()
      chatStateStoreClient.update = jest.fn()

      await UserLanguage.verify('test_chat_switch_request', 'chinese better')

      // Should call language switch detection and return early
      expect(mockCheckLanguageSwitchRequest).toHaveBeenCalledWith('test_chat_switch_request', 'chinese better')
      expect(chatStateStoreClient.increaseNodeCount).not.toHaveBeenCalled()

      mockCheckLanguageSwitchRequest.mockRestore()
    })

    it('should continue normal detection when no switch request detected', async () => {
      // Mock language switch detection
      const mockCheckLanguageSwitchRequest = jest.spyOn(UserLanguage, 'checkLanguageSwitchRequest')
      mockCheckLanguageSwitchRequest.mockResolvedValue(false)

      chatStateStoreClient.increaseNodeCount = jest.fn()
      chatStateStoreClient.getNodeCount = jest.fn().mockResolvedValue(1) // Not trigger detection
      chatHistoryServiceClient.getUserMessages = jest.fn()
      chatStateStoreClient.update = jest.fn()

      await UserLanguage.verify('test_chat_no_switch', 'Hello teacher')

      // Should call language switch detection but continue with normal flow
      expect(mockCheckLanguageSwitchRequest).toHaveBeenCalledWith('test_chat_no_switch', 'Hello teacher')
      expect(chatStateStoreClient.increaseNodeCount).toHaveBeenCalled()

      mockCheckLanguageSwitchRequest.mockRestore()
    })
  })

  describe('Edge Cases', () => {
    it('should handle empty user messages', async () => {
      // Mock dependencies
      chatStateStoreClient.increaseNodeCount = jest.fn()
      chatStateStoreClient.getNodeCount = jest.fn().mockResolvedValue(3)
      chatHistoryServiceClient.getUserMessages = jest.fn().mockResolvedValue([])
      chatStateStoreClient.getFlags = jest.fn().mockResolvedValue({ language: undefined })
      chatStateStoreClient.update = jest.fn()

      await UserLanguage.verify('test_chat_empty')

      // Should not update language with empty messages
      expect(chatStateStoreClient.update).not.toHaveBeenCalled()
    })

    it('should handle messages with only special characters', async () => {
      // Mock dependencies
      chatStateStoreClient.increaseNodeCount = jest.fn()
      chatStateStoreClient.getNodeCount = jest.fn().mockResolvedValue(3)
      chatHistoryServiceClient.getUserMessages = jest.fn().mockResolvedValue([
        '!!!',
        '???',
        '...',
        '123456'
      ])
      chatStateStoreClient.getFlags = jest.fn().mockResolvedValue({ language: undefined })
      chatStateStoreClient.update = jest.fn()

      await UserLanguage.verify('test_chat_special')

      // Should not update language with only special characters
      expect(chatStateStoreClient.update).not.toHaveBeenCalled()
    })
  })

  describe('Language Switch Request Tests', () => {
    beforeEach(() => {
      // Mock LLM for language switch detection
      jest.clearAllMocks()
    })

    it('should detect Chinese switch request', async () => {
      // Mock dependencies
      chatHistoryServiceClient.getRecentConversations = jest.fn().mockResolvedValue([
        { role: 'user', content: 'Hello teacher' },
        { role: 'assistant', content: 'Hi there! How can I help you?' },
        { role: 'user', content: 'chinese better' }
      ])
      chatStateStoreClient.update = jest.fn()

      // Mock LLM directly in the module
      // eslint-disable-next-line @typescript-eslint/no-require-imports
      const { LLM } = require('lib/ai/llm/llm_model')
      const mockPredict = jest.fn().mockResolvedValue('SWITCH_TO_CHINESE')
      LLM.mockImplementation(() => ({ predict: mockPredict }))

      const result = await UserLanguage.checkLanguageSwitchRequest('test_chat', 'chinese better')

      expect(result).toBe(true)
      expect(chatStateStoreClient.update).toHaveBeenCalledWith('test_chat', {
        state: { language: UserLanguage.Language_ZH }
      })
    })

    it('should detect English switch request', async () => {
      // Mock dependencies
      chatHistoryServiceClient.getRecentConversations = jest.fn().mockResolvedValue([
        { role: 'user', content: '老师好' },
        { role: 'assistant', content: '你好！有什么可以帮助你的吗？' },
        { role: 'user', content: 'english please' }
      ])
      chatStateStoreClient.update = jest.fn()

      // Mock LLM directly in the module
      // eslint-disable-next-line @typescript-eslint/no-require-imports
      const { LLM } = require('lib/ai/llm/llm_model')
      const mockPredict = jest.fn().mockResolvedValue('SWITCH_TO_ENGLISH')
      LLM.mockImplementation(() => ({ predict: mockPredict }))

      const result = await UserLanguage.checkLanguageSwitchRequest('test_chat', 'english please')

      expect(result).toBe(true)
      expect(chatStateStoreClient.update).toHaveBeenCalledWith('test_chat', {
        state: { language: UserLanguage.Language_EN }
      })
    })

    it('should not switch when no language keywords present', async () => {
      const result = await UserLanguage.checkLanguageSwitchRequest('test_chat', 'How are you today?')

      expect(result).toBe(false)
    })

    it('should not switch when LLM determines no switch intent', async () => {
      // Mock dependencies
      chatHistoryServiceClient.getRecentConversations = jest.fn().mockResolvedValue([
        { role: 'user', content: 'I like Chinese food' }
      ])
      chatStateStoreClient.update = jest.fn()

      // Mock LLM response
      const mockLLM = {
        predict: jest.fn().mockResolvedValue('NO_SWITCH')
      }
      jest.doMock('lib/ai/llm/llm_model', () => ({
        LLM: jest.fn().mockImplementation(() => mockLLM)
      }))

      const result = await UserLanguage.checkLanguageSwitchRequest('test_chat', 'I like Chinese food')

      expect(result).toBe(false)
      expect(chatStateStoreClient.update).not.toHaveBeenCalled()
    })
  })

  // Original test case (preserved)
  describe('Legacy Tests', () => {
    it('testVerify (original)', async () => {
      const sentenceCN = '老师，我要报名课程 It\'s 11:30 am here what is the time there now'
      const sentenceEN = '想参加五天冥想营 It\'s 11:30 am here what is the time there now'

      chatDBClient.create = jest.fn().mockResolvedValue({})
      chatStateStoreClient.getNodeCount = jest.fn().mockResolvedValue(3)
      chatHistoryServiceClient.getUserMessages = jest.fn().mockResolvedValue([sentenceEN])
      chatStateStoreClient.getFlags = jest.fn().mockResolvedValue({ language: undefined })
      chatStateStoreClient.update = jest.fn()
      chatStateStoreClient.increaseNodeCount = jest.fn()

      await UserLanguage.verify('11')

      expect(chatStateStoreClient.increaseNodeCount).toHaveBeenCalled()
    }, 60000)
  })
})