import { chatHistoryServiceClient, chatStateStoreClient } from '../../service/instance'
import { IChattingFlag } from '../../state/user_flags'
import { LLM } from 'lib/ai/llm/llm_model'
import logger from 'model/logger/logger'

export class UserLanguage {

  public static Language_EN = 'en'
  public static Language_ZH = 'cn'
  private static userMessageNodeName = 'userLanguageVerifyNodeName'

  public static async verify(chat_id: string, currentUserMessage?: string) {
    // 首先检查客户是否主动要求切换语言（如Chinese better，eng pls等）
    if (currentUserMessage && await UserLanguage.checkLanguageSwitchRequest(chat_id, currentUserMessage)) {
      return
    }

    await chatStateStoreClient.increaseNodeCount(chat_id, UserLanguage.userMessageNodeName)
    const userMessageCount = await chatStateStoreClient.getNodeCount(chat_id, UserLanguage.userMessageNodeName)

    if (userMessageCount % 3 === 0 && userMessageCount < 12) {
      const userMessages = await chatHistoryServiceClient.getUserMessages(chat_id)

      // 过滤掉图片消息（以【普通图片】开头）和视频消息（以【视频】开头）
      const filteredMessages = userMessages.filter((msg) =>
        !msg.startsWith('【普通图片】') &&
        !msg.startsWith('【视频】') &&
        !msg.startsWith('【表情】')
      )

      let userMsgStr = filteredMessages.join('')
      //移除非中英文字符
      userMsgStr = userMsgStr.replace(/[^\u4e00-\u9fff\u3400-\u4dbfa-zA-Z]/g, '')

      // 如果过滤后的有效字符太少，跳过本次语言检测
      const MIN_CHAR_THRESHOLD = 10
      if (userMsgStr.length < MIN_CHAR_THRESHOLD) {
        return
      }

      // 计算中文和英文字符数量
      let chineseCount = 0
      let englishCount = 0
      const totalLength = userMsgStr.length

      for (const char of userMsgStr) {
        // 判断是否为中文字符（包括更全面的Unicode范围）
        if (/[\u4e00-\u9fff\u3400-\u4dbf\u20000-\u2a6df\u2a700-\u2b73f\u2b740-\u2b81f\u2b820-\u2ceaf]/.test(char)) {
          chineseCount++
        }
        // 判断是否为英文字符
        else if (/[a-zA-Z]/.test(char)) {
          englishCount++
        }
      }

      // 获取当前语言设置，避免频繁切换
      const currentLanguage = await UserLanguage.getLanguage(chat_id)

      // 如果中文或英文比例大于60%，则认为该客户的语言是中文或英文
      const chineseRatio = chineseCount / totalLength
      const englishRatio = englishCount / totalLength

      // 增加语言稳定性：如果当前已有语言设置，需要更高的阈值才能切换
      const switchThreshold = currentLanguage !== UserLanguage.Language_EN ? 0.7 : 0.6
      const confirmThreshold = 0.6

      if (chineseRatio > (currentLanguage === UserLanguage.Language_ZH ? confirmThreshold : switchThreshold)) {
        await chatStateStoreClient.update(chat_id, {
          state:<IChattingFlag>{
            language:  UserLanguage.Language_ZH,
          }
        })
      } else if (englishRatio > (currentLanguage === UserLanguage.Language_EN ? confirmThreshold : switchThreshold)) {
        await chatStateStoreClient.update(chat_id, { state:<IChattingFlag> { language: UserLanguage.Language_EN } })
      }
    }
  }

  public static async getLanguage(chatId: string) {
    const language = (await chatStateStoreClient.getFlags<IChattingFlag>(chatId)).language
    if (!language) {
      return UserLanguage.Language_EN
    }
    return language
  }

  public static async getLanguageSetting(chatId: string) {
    const language = (await chatStateStoreClient.getFlags<IChattingFlag>(chatId)).language
    const languageMap = {
      [UserLanguage.Language_ZH]: '**請使用繁體中文輸出**',
      [UserLanguage.Language_EN]: '**must output with english**'
    }
    return language ? languageMap[language] : ''
  }
  /**
   * 检查客户是否主动要求切换语言
   */
  public static async checkLanguageSwitchRequest(chat_id: string, currentMessage: string): Promise<boolean> {
    const languageKeywords = [
      'chinese', 'english', 'mandarin', 'cantonese', 'traditional', 'simplified', 'eng',
      '中文', '英文', '英语', '中国话', '普通话', '繁体', '简体',
      'switch language', 'change language', 'language preference',
      '切换语言', '换语言', '语言偏好'
    ]

    const hasLanguageKeyword = languageKeywords.some((keyword) =>
      currentMessage.toLowerCase().includes(keyword.toLowerCase())
    )

    if (!hasLanguageKeyword) {
      return false
    }

    // 获取最近几轮对话作为上下文
    const recentMessages = await chatHistoryServiceClient.getRecentConversations(chat_id, 3) // 最近3轮对话
    const context = recentMessages.map((msg) => `${msg.role}: ${msg.content}`).join('\n')

    // 让AI分析客户是否真的想切换语言
    const analysisPrompt = `
# 语言切换意图分析

## 任务
分析客户是否明确表达了想要切换对话语言的意图。

## 对话历史
${context}

## 当前客户消息
${currentMessage}

## 分析规则
1. 客户明确说要用中文/英文交流
2. 客户说当前语言不方便，想换语言
3. 客户直接要求"请用中文回复"或"please reply in English"
4. 客户说"chinese better"、"english please"等明确切换意图

## 输出格式
只输出以下之一：
- SWITCH_TO_CHINESE：客户想切换到中文
- SWITCH_TO_ENGLISH：客户想切换到英文
- NO_SWITCH：客户没有切换语言的意图
`

    try {
      const llm = new LLM({
        temperature: 0,
        max_tokens: 50,
        meta: { promptName: 'language_switch_detection', chat_id, description: '语言切换检测' }
      })

      const result = await llm.predict(analysisPrompt)
      const trimmedResult = result.trim()

      if (trimmedResult === 'SWITCH_TO_CHINESE') {
        await UserLanguage.switchLanguage(chat_id, UserLanguage.Language_ZH)
        return true
      } else if (trimmedResult === 'SWITCH_TO_ENGLISH') {
        await UserLanguage.switchLanguage(chat_id, UserLanguage.Language_EN)
        return true
      }

      return false
    } catch (error) {
      console.error('语言切换检测失败:', error)
      return false
    }
  }

  /**
   * 切换语言设置
   */
  private static async switchLanguage(chat_id: string, targetLanguage: string) {
    await chatStateStoreClient.update(chat_id, {
      state: <IChattingFlag>{
        language: targetLanguage,
      }
    })

    // 记录语言切换日志
    logger.log(`Language switched for chat ${chat_id} to ${targetLanguage}`)
  }
}